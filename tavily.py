# SPDX-License-Identifier: AGPL-3.0-or-later
"""
Tavily 搜索引擎
"""

from json import loads
import os
from urllib.parse import urlencode
from searx.engines import categories
from searx.utils import html_to_text

# 引擎元数据，定义引擎支持的类别和分页能力等
categories = ['general']
paging = True
language_support = False
time_range_support = True
safesearch = False

# 定义搜索URL和API密钥 - 直接写死API密钥
base_url = 'https://api.tavily.com/search'
# api_key = os.getenv('TAVILY_API_KEY', '')  # 从环境变量读取Tavily API密钥
api_key = '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'  # 直接写死API密钥，请替换为您的真实密钥

# 定义搜索请求参数
search_params = {
    'api_key': api_key,
    'query': '',           # 将由SearXNG填充
    'search_depth': 'basic',
    'include_answer': 'false',
    'include_images': 'false',
    'max_results': 5
}

def request(query, params):
    """
    构建搜索请求
    """
    search_params['query'] = query

    # 支持分页
    if params.get('pageno', 1) > 1:
        search_params['page'] = params.get('pageno')

    # 支持时间范围
    if params.get('time_range'):
        time_map = {
            'day': 'day',
            'week': 'week',
            'month': 'month', 
            'year': 'year'
        }
        time_range = params.get('time_range')
        if time_range in time_map:
            search_params['time_range'] = time_map[time_range]

    # 构建URL
    params['url'] = f"{base_url}?{urlencode(search_params)}"
    
    # 设置请求头
    params['headers']['Content-Type'] = 'application/json'
    return params

def response(resp):
    """
    处理搜索结果
    """
    results = []
    
    if resp.status_code != 200:
        return results
    
    # 解析JSON响应
    response_json = loads(resp.text)
    
    # 确保响应包含结果
    if 'results' not in response_json:
        return results
    
    # 处理每个搜索结果
    for item in response_json['results']:
        # 创建结果字典
        result = {
            'title': item.get('title', ''),
            'url': item.get('url', ''),
            'content': item.get('content', '')
        }
        
        # 如果响应包含原始内容，添加为HTML内容
        if 'raw_content' in item and item['raw_content']:
            result['content_html'] = item['raw_content']
            if not result['content']:
                result['content'] = html_to_text(item['raw_content'])
        
        # 添加到结果列表
        results.append(result)
    
    # 返回结果列表
    return results 