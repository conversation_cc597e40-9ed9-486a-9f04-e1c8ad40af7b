version: '3'

services:
  searxng:
    image: searxng/searxng:latest
    container_name: searxng
    ports:
      - "8080:8080"
    volumes:
      - ./searxng-settings.yml:/etc/searxng/settings.yml
      - ./tavily.py:/usr/local/searxng/searx/engines/tavily.py
    environment:
      - SEARXNG_BASE_URL=http://localhost:8080/
      - SEARXNG_SECRET_KEY=${SEARXNG_SECRET_KEY:-"自动生成一个密钥，替换此文本"}
      - SEARXNG_BIND_ADDRESS=0.0.0.0
      - TAVILY_API_KEY=${TAVILY_API_KEY:-"你的Tavily API密钥"}
    restart: unless-stopped
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "1"

  # 可选：Redis服务用于缓存和限流
  redis:
    image: redis:alpine
    container_name: searxng_redis
    command: redis-server --save 3600 1 --loglevel warning
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - searxng_net

volumes:
  redis_data:

networks:
  searxng_net:
    driver: bridge 