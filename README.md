# SearXNG + Tavily/Brave 集成指南

这个项目增强了SearXNG，添加了Tavily和Brave搜索引擎的支持，使您能够获得更多样化的搜索结果。

## 概述

- **SearXNG**：一个隐私保护型元搜索引擎
- **Tavily**：专为AI设计的搜索API，提供高质量和实时的结果
- **Brave**：注重隐私的搜索引擎

## 环境要求

### Tavily集成要求
1. 有效的Tavily API密钥（从[Tavily网站](https://tavily.com/)获取）
2. 设置环境变量`TAVILY_API_KEY`

## 安装

### Docker安装
```bash
# 拉取最新的SearXNG镜像
docker pull searxng/searxng

# 运行SearXNG容器
docker run -d \
  --name searxng \
  -p 8080:8080 \
  -v $(pwd)/searxng-settings.yml:/etc/searxng/settings.yml \
  -v $(pwd)/tavily.py:/usr/local/searxng/searx/engines/tavily.py \
  -e TAVILY_API_KEY=your_tavily_api_key \
  searxng/searxng
```

### 本地安装
1. 克隆SearXNG仓库
```bash
git clone https://github.com/searxng/searxng.git
cd searxng
```

2. 复制配置文件
```bash
cp searxng-settings.yml /etc/searxng/settings.yml
cp tavily.py searx/engines/
```

3. 设置环境变量
```bash
export TAVILY_API_KEY=your_tavily_api_key
```

4. 运行SearXNG
```bash
python searx/webapp.py
```

## 使用方法

### 使用Tavily引擎搜索
- 通过网页界面：选择"Tavily"作为搜索引擎
- 通过URL参数：`?q=your+query&engines=tavily`
- 使用快捷方式：`!tav your query`

### 使用Brave引擎搜索
- 通过网页界面：选择"Brave"作为搜索引擎
- 通过URL参数：`?q=your+query&engines=brave`
- 使用快捷方式：`!br your query`

## API使用

SearXNG提供了一个简单的API，可以返回JSON格式的结果：

```
http://your-searxng-instance:8080/search?q=your+query&format=json&engines=tavily
```

## 配置说明

### Tavily引擎配置
在`searxng-settings.yml`中，您可以调整以下Tavily相关设置：
- `max_results`: 返回结果数量
- `search_depth`: 搜索深度（basic/advanced）
- `include_answer`: 是否包含生成的答案
- `include_images`: 是否包含图片结果

### Brave引擎配置
- `time_range_support`: 时间范围过滤
- `categories`: 搜索类别

## 故障排除

如果遇到以下问题：

1. **Tavily返回空结果**：
   - 检查API密钥是否正确设置
   - 确认网络连接可以访问api.tavily.com
   - 查看SearXNG日志获取更多信息

2. **Brave搜索问题**：
   - 某些区域可能无法访问Brave搜索API
   - 确认Brave搜索引擎已启用

## 进阶使用

### 组合搜索
您可以同时使用多个搜索引擎：
```
?q=your+query&engines=tavily,brave,google
```

### 定制引擎设置
您可以根据需要修改`tavily.py`或调整`searxng-settings.yml`中的引擎配置以满足您的特定需求。

## 贡献与支持

欢迎提交Issue或PR，共同改进这个项目。 